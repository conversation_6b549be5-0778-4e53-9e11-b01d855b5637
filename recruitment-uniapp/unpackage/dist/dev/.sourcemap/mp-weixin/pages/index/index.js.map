{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/index/index.vue?34d3", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/index/index.vue?c281", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/index/index.vue?8ba3", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/index/index.vue?ab9d", "uni-app:///pages/index/index.vue", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/index/index.vue?9c7f", "webpack:////Users/<USER>/projects/recruitment-platform/recruitment-uniapp/pages/index/index.vue?46ea"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "RegionPicker", "data", "jobList", "loading", "refreshing", "hasMore", "pageNum", "pageSize", "sortBy", "salaryRange", "industryFilter", "jobTypeFilter", "currentLocation", "selectedLocation", "showLocationPicker", "showSortPicker", "showSalaryFilter", "showIndustryFilter", "showJobTypeFilter", "sortOptions", "label", "value", "salaryOptions", "computed", "sortText", "salaryFilterText", "industryFilterText", "jobTypeFilterText", "onLoad", "onShow", "methods", "formatSalary", "formatTime", "initLocation", "loadJobList", "isRefresh", "params", "industry", "jobType", "regionId", "jobApi", "res", "newList", "console", "onRefresh", "loadMore", "goToSearch", "uni", "url", "goToJobDetail", "confirmLocation", "storage", "selectSort", "selectSalaryRange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAm0B,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACkJv1B;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC,cACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,gBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EACAE;IACAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MAAA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;IACAC;IAEA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBACA;kBACA;kBACA;gBACA;gBAEAC;kBACA9B;kBACAC;kBACAC;kBACAC;kBACA4B;kBACAC;kBACAC,qFACA,mEACA;gBACA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBAEA;kBACAC;kBAEA;oBACA;kBACA;oBACA;kBACA;kBAEA;kBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACA,4FACA,iEACA,yEACA;MAEA;MACAC;QACArD;MAAA,GACA,uBACA;MAEA;MACA;IACA;IAEA;IACAsD;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3VA;AAAA;AAAA;AAAA;AAA0jD,CAAgB,07CAAG,EAAC,C;;;;;;;;;;;ACA9kD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.jobList, function (job, __i0__) {\n    var $orig = _vm.__get_orig(job)\n    var m0 = _vm.formatSalary(job.minSalary, job.maxSalary, job.salaryType)\n    var m1 = _vm.formatTime(job.publishTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  var g0 = !_vm.hasMore ? _vm.jobList.length : null\n  var g1 = !_vm.loading && _vm.jobList.length === 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showLocationPicker = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showSortPicker = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showSalaryFilter = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showIndustryFilter = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.showJobTypeFilter = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.showLocationPicker = false\n    }\n    _vm.e6 = function ($event) {\n      _vm.showSalaryFilter = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"job-list-container\">\n    <!-- 搜索栏 -->\n    <view class=\"search-bar\">\n      <view class=\"search-input\" @click=\"goToSearch\">\n        <text class=\"iconfont icon-search\"></text>\n        <text class=\"placeholder\">搜索职位、公司</text>\n      </view>\n      <view class=\"location-btn\" @click=\"showLocationPicker = true\">\n        <text class=\"location-text\">{{ currentLocation }}</text>\n        <text class=\"iconfont icon-arrow-down\"></text>\n      </view>\n    </view>\n\n    <!-- 筛选栏 -->\n    <view class=\"filter-bar\">\n      <view class=\"filter-item\" @click=\"showSortPicker = true\">\n        <text>{{ sortText }}</text>\n        <text class=\"iconfont icon-arrow-down\"></text>\n      </view>\n      <view class=\"filter-item\" @click=\"showSalaryFilter = true\">\n        <text>{{ salaryFilterText }}</text>\n        <text class=\"iconfont icon-arrow-down\"></text>\n      </view>\n      <view class=\"filter-item\" @click=\"showIndustryFilter = true\">\n        <text>{{ industryFilterText }}</text>\n        <text class=\"iconfont icon-arrow-down\"></text>\n      </view>\n      <view class=\"filter-item\" @click=\"showJobTypeFilter = true\">\n        <text>{{ jobTypeFilterText }}</text>\n        <text class=\"iconfont icon-arrow-down\"></text>\n      </view>\n    </view>\n\n    <!-- 职位列表 -->\n    <scroll-view class=\"job-list\" scroll-y @scrolltolower=\"loadMore\" refresher-enabled :refresher-triggered=\"refreshing\"\n      @refresherrefresh=\"onRefresh\">\n      <view v-for=\"job in jobList\" :key=\"job.id\" class=\"job-item\" @click=\"goToJobDetail(job.id)\">\n        <view class=\"job-header\">\n          <view class=\"job-info\">\n            <text class=\"job-title\">{{ job.title }}</text>\n            <text class=\"salary\">{{ formatSalary(job.minSalary, job.maxSalary, job.salaryType) }}</text>\n          </view>\n          <view class=\"company-logo\">\n            <image :src=\"job.companyLogo || '/static/default-company.png'\" mode=\"aspectFill\"></image>\n          </view>\n        </view>\n\n        <view class=\"job-tags\">\n          <text v-for=\"tag in job.tags\" :key=\"tag\" class=\"tag\">\n            {{ tag }}\n          </text>\n        </view>\n\n        <view class=\"job-meta\">\n          <view class=\"meta-item\">\n            <text class=\"iconfont icon-location\"></text>\n            <text>{{ job.location }}</text>\n          </view>\n          <view class=\"meta-item\">\n            <text class=\"iconfont icon-experience\"></text>\n            <text>{{ job.experience }}</text>\n          </view>\n          <view class=\"meta-item\">\n            <text class=\"iconfont icon-education\"></text>\n            <text>{{ job.education }}</text>\n          </view>\n        </view>\n\n        <view class=\"job-footer\">\n          <view class=\"company-info\">\n            <text class=\"company-name\">{{ job.companyName }}</text>\n            <text class=\"company-scale\">{{ job.companyScale }}</text>\n          </view>\n          <view class=\"publish-time\">\n            <text>{{ formatTime(job.publishTime) }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 加载更多 -->\n      <view v-if=\"hasMore\" class=\"load-more\">\n        <text>{{ loading ? '加载中...' : '上拉加载更多' }}</text>\n      </view>\n      <view v-else-if=\"jobList.length > 0\" class=\"no-more\">\n        <text>没有更多数据了</text>\n      </view>\n\n      <!-- 空状态 -->\n      <view v-if=\"!loading && jobList.length === 0\" class=\"empty-state\">\n        <image src=\"/static/empty-job.png\" class=\"empty-img\"></image>\n        <text class=\"empty-text\">暂无招聘信息</text>\n        <button class=\"refresh-btn\" @click=\"onRefresh\">刷新试试</button>\n      </view>\n    </scroll-view>\n\n    <!-- 地区选择弹窗 -->\n    <u-popup v-model=\"showLocationPicker\" mode=\"bottom\" height=\"60%\" border-radius=\"20\">\n      <view class=\"location-picker\">\n        <view class=\"picker-header\">\n          <text class=\"cancel-btn\" @click=\"showLocationPicker = false\">取消</text>\n          <text class=\"title\">选择地区</text>\n          <text class=\"confirm-btn\" @click=\"confirmLocation\">确定</text>\n        </view>\n        <RegionPicker v-model=\"selectedLocation\" />\n      </view>\n    </u-popup>\n\n    <!-- 排序选择弹窗 -->\n    <u-popup v-model=\"showSortPicker\" mode=\"bottom\" height=\"40%\" border-radius=\"20\">\n      <view class=\"sort-picker\">\n        <view class=\"picker-header\">\n          <text class=\"title\">排序方式</text>\n        </view>\n        <view class=\"sort-options\">\n          <view v-for=\"option in sortOptions\" :key=\"option.value\" class=\"sort-option\"\n            :class=\"{ active: sortBy === option.value }\" @click=\"selectSort(option.value)\">\n            <text>{{ option.label }}</text>\n            <text v-if=\"sortBy === option.value\" class=\"iconfont icon-check\"></text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n\n    <!-- 薪资筛选弹窗 -->\n    <u-popup v-model=\"showSalaryFilter\" mode=\"bottom\" height=\"50%\" border-radius=\"20\">\n      <view class=\"salary-filter\">\n        <view class=\"picker-header\">\n          <text class=\"cancel-btn\" @click=\"showSalaryFilter = false\">取消</text>\n          <text class=\"title\">薪资范围</text>\n          <text class=\"confirm-btn\" @click=\"confirmSalaryFilter\">确定</text>\n        </view>\n        <view class=\"salary-options\">\n          <view v-for=\"option in salaryOptions\" :key=\"option.value\" class=\"salary-option\"\n            :class=\"{ active: salaryRange === option.value }\" @click=\"selectSalaryRange(option.value)\">\n            <text>{{ option.label }}</text>\n            <text v-if=\"salaryRange === option.value\" class=\"iconfont icon-check\"></text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script>\nimport RegionPicker from '@/components/RegionPicker.vue'\nimport { jobApi } from '@/utils/api.js'\nimport { formatSalary, formatTime, showToast, showLoading, hideLoading, storage } from '@/utils/utils.js'\n\nexport default {\n  name: 'JobList',\n  components: {\n    RegionPicker\n  },\n  data() {\n    return {\n      jobList: [],\n      loading: false,\n      refreshing: false,\n      hasMore: true,\n      pageNum: 1,\n      pageSize: 10,\n\n      // 筛选条件\n      sortBy: 'publishTime', // publishTime, salary, distance\n      salaryRange: '',\n      industryFilter: '',\n      jobTypeFilter: '',\n\n      // 地区相关\n      currentLocation: '全国',\n      selectedLocation: {},\n      showLocationPicker: false,\n\n      // 弹窗显示状态\n      showSortPicker: false,\n      showSalaryFilter: false,\n      showIndustryFilter: false,\n      showJobTypeFilter: false,\n\n      // 选项数据\n      sortOptions: [\n        { label: '发布时间', value: 'publishTime' },\n        { label: '薪资高低', value: 'salary' },\n        { label: '距离远近', value: 'distance' }\n      ],\n      salaryOptions: [\n        { label: '不限', value: '' },\n        { label: '3K以下', value: '0-3000' },\n        { label: '3K-5K', value: '3000-5000' },\n        { label: '5K-8K', value: '5000-8000' },\n        { label: '8K-12K', value: '8000-12000' },\n        { label: '12K-20K', value: '12000-20000' },\n        { label: '20K以上', value: '20000-999999' }\n      ]\n    }\n  },\n  computed: {\n    sortText() {\n      const option = this.sortOptions.find(item => item.value === this.sortBy)\n      return option ? option.label : '排序'\n    },\n    salaryFilterText() {\n      const option = this.salaryOptions.find(item => item.value === this.salaryRange)\n      return option ? option.label : '薪资'\n    },\n    industryFilterText() {\n      return this.industryFilter || '行业'\n    },\n    jobTypeFilterText() {\n      return this.jobTypeFilter || '性质'\n    }\n  },\n  onLoad() {\n    this.initLocation()\n    this.loadJobList()\n  },\n  onShow() {\n    // 从搜索页返回时刷新列表\n    if (this.needRefresh) {\n      this.onRefresh()\n      this.needRefresh = false\n    }\n  },\n  methods: {\n    formatSalary,\n    formatTime,\n\n    // 初始化地区\n    initLocation() {\n      const savedLocation = storage.get('currentLocation')\n      if (savedLocation) {\n        this.currentLocation = savedLocation.name || '全国'\n        this.selectedLocation = savedLocation\n      }\n    },\n\n    // 加载职位列表\n    async loadJobList(isRefresh = false) {\n      if (this.loading) return\n\n      try {\n        this.loading = true\n        if (isRefresh) {\n          this.pageNum = 1\n          this.hasMore = true\n        }\n\n        const params = {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize,\n          sortBy: this.sortBy,\n          salaryRange: this.salaryRange,\n          industry: this.industryFilter,\n          jobType: this.jobTypeFilter,\n          regionId: (this.selectedLocation.district && this.selectedLocation.district.id) ||\n            (this.selectedLocation.city && this.selectedLocation.city.id) ||\n            (this.selectedLocation.province && this.selectedLocation.province.id)\n        }\n\n        const res = await jobApi.getJobList(params)\n\n        if (res.code === 200) {\n          const newList = res.data.list || []\n\n          if (isRefresh) {\n            this.jobList = newList\n          } else {\n            this.jobList = [...this.jobList, ...newList]\n          }\n\n          this.hasMore = newList.length === this.pageSize\n          this.pageNum++\n        } else {\n          showToast(res.msg || '加载失败')\n        }\n      } catch (error) {\n        console.error('加载职位列表失败:', error)\n        showToast('加载失败，请重试')\n      } finally {\n        this.loading = false\n        this.refreshing = false\n      }\n    },\n\n    // 刷新\n    onRefresh() {\n      this.refreshing = true\n      this.loadJobList(true)\n    },\n\n    // 加载更多\n    loadMore() {\n      if (this.hasMore && !this.loading) {\n        this.loadJobList()\n      }\n    },\n\n    // 跳转到搜索页\n    goToSearch() {\n      uni.navigateTo({\n        url: '/pages/search/search'\n      })\n    },\n\n    // 跳转到职位详情\n    goToJobDetail(jobId) {\n      uni.navigateTo({\n        url: `/pages/job/detail?id=${jobId}`\n      })\n    },\n\n    // 确认地区选择\n    confirmLocation() {\n      const locationText = (this.selectedLocation.district && this.selectedLocation.district.name) ||\n        (this.selectedLocation.city && this.selectedLocation.city.name) ||\n        (this.selectedLocation.province && this.selectedLocation.province.name) ||\n        '全国'\n\n      this.currentLocation = locationText\n      storage.set('currentLocation', {\n        name: locationText,\n        ...this.selectedLocation\n      })\n\n      this.showLocationPicker = false\n      this.onRefresh()\n    },\n\n    // 选择排序方式\n    selectSort(value) {\n      this.sortBy = value\n      this.showSortPicker = false\n      this.onRefresh()\n    },\n\n    // 选择薪资范围\n    selectSalaryRange(value) {\n      this.salaryRange = value\n    },\n\n    // 确认薪资筛选\n    confirmSalaryFilter() {\n      this.showSalaryFilter = false\n      this.onRefresh()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.job-list-container {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background: #f8f9fa;\n\n  .search-bar {\n    display: flex;\n    align-items: center;\n    padding: 20rpx 32rpx;\n    background: #fff;\n    border-bottom: 2rpx solid #f0f0f0;\n\n    .search-input {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      height: 72rpx;\n      padding: 0 24rpx;\n      background: #f8f9fa;\n      border-radius: 36rpx;\n      margin-right: 20rpx;\n\n      .iconfont {\n        font-size: 32rpx;\n        color: #999;\n        margin-right: 16rpx;\n      }\n\n      .placeholder {\n        font-size: 28rpx;\n        color: #999;\n      }\n    }\n\n    .location-btn {\n      display: flex;\n      align-items: center;\n      padding: 0 16rpx;\n\n      .location-text {\n        font-size: 28rpx;\n        color: #333;\n        margin-right: 8rpx;\n      }\n\n      .iconfont {\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n  }\n\n  .filter-bar {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 32rpx;\n    background: #fff;\n    border-bottom: 2rpx solid #f0f0f0;\n\n    .filter-item {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 28rpx;\n      color: #333;\n\n      .iconfont {\n        font-size: 20rpx;\n        color: #999;\n        margin-left: 8rpx;\n      }\n    }\n  }\n\n  .job-list {\n    flex: 1;\n    padding: 0 32rpx;\n\n    .job-item {\n      background: #fff;\n      border-radius: 16rpx;\n      padding: 32rpx;\n      margin: 24rpx 0;\n      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n\n      .job-header {\n        display: flex;\n        align-items: flex-start;\n        justify-content: space-between;\n        margin-bottom: 24rpx;\n\n        .job-info {\n          flex: 1;\n\n          .job-title {\n            display: block;\n            font-size: 32rpx;\n            font-weight: 600;\n            color: #333;\n            margin-bottom: 12rpx;\n          }\n\n          .salary {\n            display: block;\n            font-size: 28rpx;\n            color: #ff4757;\n            font-weight: 600;\n          }\n        }\n\n        .company-logo {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 12rpx;\n          overflow: hidden;\n\n          image {\n            width: 100%;\n            height: 100%;\n          }\n        }\n      }\n\n      .job-tags {\n        display: flex;\n        flex-wrap: wrap;\n        gap: 16rpx;\n        margin-bottom: 24rpx;\n\n        .tag {\n          padding: 8rpx 16rpx;\n          background: #f0f8ff;\n          color: #007aff;\n          font-size: 24rpx;\n          border-radius: 8rpx;\n        }\n      }\n\n      .job-meta {\n        display: flex;\n        align-items: center;\n        gap: 32rpx;\n        margin-bottom: 24rpx;\n\n        .meta-item {\n          display: flex;\n          align-items: center;\n          font-size: 24rpx;\n          color: #666;\n\n          .iconfont {\n            font-size: 24rpx;\n            margin-right: 8rpx;\n          }\n        }\n      }\n\n      .job-footer {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .company-info {\n          .company-name {\n            font-size: 28rpx;\n            color: #333;\n            margin-right: 16rpx;\n          }\n\n          .company-scale {\n            font-size: 24rpx;\n            color: #666;\n          }\n        }\n\n        .publish-time {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n    }\n\n    .load-more,\n    .no-more {\n      text-align: center;\n      padding: 40rpx 0;\n      font-size: 28rpx;\n      color: #999;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 120rpx 0;\n\n      .empty-img {\n        width: 200rpx;\n        height: 200rpx;\n        margin-bottom: 40rpx;\n      }\n\n      .empty-text {\n        display: block;\n        font-size: 28rpx;\n        color: #999;\n        margin-bottom: 40rpx;\n      }\n\n      .refresh-btn {\n        width: 200rpx;\n        height: 64rpx;\n        background: #007aff;\n        color: #fff;\n        border: none;\n        border-radius: 32rpx;\n        font-size: 28rpx;\n      }\n    }\n  }\n}\n\n// 弹窗样式\n.location-picker,\n.sort-picker,\n.salary-filter {\n  .picker-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 32rpx;\n    border-bottom: 2rpx solid #f0f0f0;\n\n    .cancel-btn,\n    .confirm-btn {\n      font-size: 28rpx;\n      color: #007aff;\n    }\n\n    .title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n  }\n}\n\n.sort-options,\n.salary-options {\n\n  .sort-option,\n  .salary-option {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 32rpx;\n    border-bottom: 2rpx solid #f8f9fa;\n    font-size: 28rpx;\n    color: #333;\n\n    &.active {\n      color: #007aff;\n      background: #f0f8ff;\n    }\n\n    .iconfont {\n      font-size: 32rpx;\n      color: #007aff;\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1756771055874\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}