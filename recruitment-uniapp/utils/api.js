// API配置
//export const BASE_URL = "http://192.168.31.66:7788";
export const BASE_URL = "http://127.0.0.1:7799";
// 请求封装
const request = (options) => {
  const fullUrl = BASE_URL + options.url;

  // 获取本地存储的token
  const token = uni.getStorageSync("token");

  // 构建请求头
  const headers = {
    "Content-Type": "application/json",
    ...options.header,
  };

  // 如果有token，添加到请求头
  if (token) {
    headers.Authorization = `Bearer ${token}`;
    console.log("添加Authorization头部:", headers.Authorization);
  } else {
    console.log("未找到token，跳过Authorization头部");
  }

  console.log("发起请求:", {
    url: fullUrl,
    method: options.method || "GET",
    data: options.data || {},
    headers: headers,
  });

  return new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method: options.method || "GET",
      data: options.data || {},
      header: headers,
      success: (res) => {
        console.log("请求响应:", {
          url: fullUrl,
          statusCode: res.statusCode,
          data: res.data,
        });

        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data);
          } else if (res.data.code === 401) {
            // 401未授权，跳转到登录页
            console.warn("用户未授权，跳转到登录页", res.data);
            uni.showToast({
              title: res.data.msg || "请先登录",
              icon: "none",
            });
            // 使用auth工具清除登录信息
            const { auth } = require("./utils.js");
            auth.clearToken();
            auth.clearUserInfo();
            // 跳转到登录页
            uni.reLaunch({
              url: "/pages/auth/login",
            });
            reject(res.data);
          } else {
            console.error("业务错误:", res.data);
            uni.showToast({
              title: res.data.msg || "请求失败",
              icon: "none",
            });
            reject(res.data);
          }
        } else {
          console.error("HTTP错误:", res);
          uni.showToast({
            title: "网络请求失败",
            icon: "none",
          });
          reject(res);
        }
      },
      fail: (err) => {
        console.error("请求失败:", err);
        uni.showToast({
          title: "网络连接失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
};

// 用户认证相关API
export const authApi = {
  // 微信授权登录
  wechatLogin(code) {
    return request({
      url: "/miniapp/auth/wechat",
      method: "POST",
      data: { code },
    });
  },

  // 手机号一键登录（微信授权方式）
  phoneLoginByWechat(encryptedData, iv, sessionKey) {
    return request({
      url: "/miniapp/auth/phone/wechat",
      method: "POST",
      data: { encryptedData, iv, sessionKey },
    });
  },

  // 手机号一键登录（验证码方式）
  phoneLogin(phone, code) {
    return request({
      url: "/miniapp/auth/phone",
      method: "POST",
      data: { phone, code },
    });
  },

  // 发送短信验证码
  sendSmsCode(phone) {
    return request({
      url: "/miniapp/auth/sms/send",
      method: "POST",
      data: { phone },
    });
  },

  // 用户注册
  register(userInfo) {
    return request({
      url: "/miniapp/auth/register",
      method: "POST",
      data: userInfo,
    });
  },

  // 获取用户信息
  getUserInfo(seekerId) {
    return request({
      url: `/miniapp/user/${seekerId}`,
    });
  },
};

// 招聘信息相关API
export const jobApi = {
  // 获取招聘列表
  getJobList(params) {
    return request({
      url: "/miniapp/positions",
      data: params,
    });
  },

  // 获取招聘详情
  getJobDetail(positionId) {
    return request({
      url: `/miniapp/position/${positionId}`,
    });
  },

  // 搜索招聘信息
  searchJobs(keyword, regionCode) {
    return request({
      url: "/miniapp/positions",
      data: { keyword, regionCode },
    });
  },

  // 获取热门职位
  getHotJobs(limit = 10) {
    return request({
      url: "/miniapp/positions/hot",
      data: { limit },
    });
  },

  // 获取最新职位
  getLatestJobs(limit = 10) {
    return request({
      url: "/miniapp/positions/latest",
      data: { limit },
    });
  },

  // 查看联系方式（需要付费或会员）
  viewContact(positionId, seekerId) {
    return request({
      url: `/miniapp/position/${positionId}/contact`,
      method: "POST",
      data: { seekerId },
    });
  },
};

// 地区相关API
export const regionApi = {
  // 获取地区列表
  getRegions(parentCode) {
    return request({
      url: "/miniapp/regions",
      data: { parentCode },
    });
  },
};

// 付费功能相关API
export const paymentApi = {
  // 获取会员套餐列表
  getMemberPackages() {
    return request({
      url: "/miniapp/packages",
    });
  },

  // 创建订单
  createOrder(orderData) {
    return request({
      url: "/miniapp/order/create",
      method: "POST",
      data: orderData,
    });
  },

  // 微信支付
  wechatPay(orderNo, openid) {
    return request({
      url: "/miniapp/pay/wechat",
      method: "POST",
      data: { orderNo, openid },
    });
  },

  // 获取用户消费记录
  getUserRecords(seekerId) {
    return request({
      url: `/miniapp/records/${seekerId}`,
    });
  },

  // 检查用户会员状态
  getMemberStatus(seekerId) {
    return request({
      url: `/miniapp/member/status/${seekerId}`,
    });
  },
};

export default request;
