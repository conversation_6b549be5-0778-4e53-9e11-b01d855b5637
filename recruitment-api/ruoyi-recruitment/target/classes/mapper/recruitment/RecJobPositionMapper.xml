<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.recruitment.mapper.RecJobPositionMapper">
    
    <resultMap type="RecJobPosition" id="RecJobPositionResult">
        <result property="positionId"    column="position_id"    />
        <result property="employerId"    column="employer_id"    />
        <result property="positionTitle"    column="position_title"    />
        <result property="positionType"    column="position_type"    />
        <result property="industry"    column="industry"    />
        <result property="workNature"    column="work_nature"    />
        <result property="salaryMin"    column="salary_min"    />
        <result property="salaryMax"    column="salary_max"    />
        <result property="salaryUnit"    column="salary_unit"    />
        <result property="workExperience"    column="work_experience"    />
        <result property="education"    column="education"    />
        <result property="workAddress"    column="work_address"    />
        <result property="regionCode"    column="region_code"    />
        <result property="jobDescription"    column="job_description"    />
        <result property="jobRequirements"    column="job_requirements"    />
        <result property="welfareBenefits"    column="welfare_benefits"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="contactWechat"    column="contact_wechat"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="viewCount"    column="view_count"    />
        <result property="applyCount"    column="apply_count"    />
        <result property="isUrgent"    column="is_urgent"    />
        <result property="isTop"    column="is_top"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="companyName"    column="company_name"    />
        <result property="regionName"    column="region_name"    />
    </resultMap>

    <sql id="selectRecJobPositionVo">
        select p.position_id, p.employer_id, p.position_title, p.position_type, p.industry, 
               p.work_nature, p.salary_min, p.salary_max, p.salary_unit, p.work_experience, 
               p.education, p.work_address, p.region_code, p.job_description, p.job_requirements, 
               p.welfare_benefits, p.contact_person, p.contact_phone, p.contact_wechat, 
               p.publish_status, p.audit_status, p.audit_remark, p.audit_time, p.audit_by, 
               p.publish_time, p.expire_time, p.view_count, p.apply_count, p.is_urgent, 
               p.is_top, p.sort_order, p.create_by, p.create_time, p.update_by, p.update_time,
               e.company_name, r.region_name
        from rec_job_position p
        left join rec_employer e on p.employer_id = e.employer_id
        left join rec_region r on p.region_code = r.region_code
    </sql>

    <select id="selectRecJobPositionList" parameterType="RecJobPosition" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        <where>  
            <if test="employerId != null "> and p.employer_id = #{employerId}</if>
            <if test="positionTitle != null  and positionTitle != ''"> and p.position_title like concat('%', #{positionTitle}, '%')</if>
            <if test="positionType != null  and positionType != ''"> and p.position_type = #{positionType}</if>
            <if test="industry != null  and industry != ''"> and p.industry = #{industry}</if>
            <if test="workNature != null  and workNature != ''"> and p.work_nature = #{workNature}</if>
            <if test="regionCode != null  and regionCode != ''"> and p.region_code = #{regionCode}</if>
            <if test="publishStatus != null "> and p.publish_status = #{publishStatus}</if>
            <if test="auditStatus != null "> and p.audit_status = #{auditStatus}</if>
            <if test="isUrgent != null "> and p.is_urgent = #{isUrgent}</if>
            <if test="isTop != null "> and p.is_top = #{isTop}</if>
            <if test="params.beginPublishTime != null and params.beginPublishTime != ''"><!-- 开始发布时间 -->
                and date_format(p.publish_time,'%y%m%d') &gt;= date_format(#{params.beginPublishTime},'%y%m%d')
            </if>
            <if test="params.endPublishTime != null and params.endPublishTime != ''"><!-- 结束发布时间 -->
                and date_format(p.publish_time,'%y%m%d') &lt;= date_format(#{params.endPublishTime},'%y%m%d')
            </if>
        </where>
        order by p.is_top desc, p.sort_order asc, p.create_time desc
    </select>
    
    <select id="selectRecJobPositionByPositionId" parameterType="Long" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        where p.position_id = #{positionId}
    </select>

    <select id="selectRecJobPositionByEmployerId" parameterType="Long" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        where p.employer_id = #{employerId}
        order by p.is_top desc, p.sort_order asc, p.create_time desc
    </select>
        
    <insert id="insertRecJobPosition" parameterType="RecJobPosition" useGeneratedKeys="true" keyProperty="positionId">
        insert into rec_job_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employerId != null">employer_id,</if>
            <if test="positionTitle != null and positionTitle != ''">position_title,</if>
            <if test="positionType != null">position_type,</if>
            <if test="industry != null">industry,</if>
            <if test="workNature != null">work_nature,</if>
            <if test="salaryMin != null">salary_min,</if>
            <if test="salaryMax != null">salary_max,</if>
            <if test="salaryUnit != null">salary_unit,</if>
            <if test="workExperience != null">work_experience,</if>
            <if test="education != null">education,</if>
            <if test="workAddress != null">work_address,</if>
            <if test="regionCode != null">region_code,</if>
            <if test="jobDescription != null">job_description,</if>
            <if test="jobRequirements != null">job_requirements,</if>
            <if test="welfareBenefits != null">welfare_benefits,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactWechat != null">contact_wechat,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="applyCount != null">apply_count,</if>
            <if test="isUrgent != null">is_urgent,</if>
            <if test="isTop != null">is_top,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employerId != null">#{employerId},</if>
            <if test="positionTitle != null and positionTitle != ''">#{positionTitle},</if>
            <if test="positionType != null">#{positionType},</if>
            <if test="industry != null">#{industry},</if>
            <if test="workNature != null">#{workNature},</if>
            <if test="salaryMin != null">#{salaryMin},</if>
            <if test="salaryMax != null">#{salaryMax},</if>
            <if test="salaryUnit != null">#{salaryUnit},</if>
            <if test="workExperience != null">#{workExperience},</if>
            <if test="education != null">#{education},</if>
            <if test="workAddress != null">#{workAddress},</if>
            <if test="regionCode != null">#{regionCode},</if>
            <if test="jobDescription != null">#{jobDescription},</if>
            <if test="jobRequirements != null">#{jobRequirements},</if>
            <if test="welfareBenefits != null">#{welfareBenefits},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactWechat != null">#{contactWechat},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="applyCount != null">#{applyCount},</if>
            <if test="isUrgent != null">#{isUrgent},</if>
            <if test="isTop != null">#{isTop},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRecJobPosition" parameterType="RecJobPosition">
        update rec_job_position
        <trim prefix="SET" suffixOverrides=",">
            <if test="employerId != null">employer_id = #{employerId},</if>
            <if test="positionTitle != null and positionTitle != ''">position_title = #{positionTitle},</if>
            <if test="positionType != null">position_type = #{positionType},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="workNature != null">work_nature = #{workNature},</if>
            <if test="salaryMin != null">salary_min = #{salaryMin},</if>
            <if test="salaryMax != null">salary_max = #{salaryMax},</if>
            <if test="salaryUnit != null">salary_unit = #{salaryUnit},</if>
            <if test="workExperience != null">work_experience = #{workExperience},</if>
            <if test="education != null">education = #{education},</if>
            <if test="workAddress != null">work_address = #{workAddress},</if>
            <if test="regionCode != null">region_code = #{regionCode},</if>
            <if test="jobDescription != null">job_description = #{jobDescription},</if>
            <if test="jobRequirements != null">job_requirements = #{jobRequirements},</if>
            <if test="welfareBenefits != null">welfare_benefits = #{welfareBenefits},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactWechat != null">contact_wechat = #{contactWechat},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="applyCount != null">apply_count = #{applyCount},</if>
            <if test="isUrgent != null">is_urgent = #{isUrgent},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where position_id = #{positionId}
    </update>

    <delete id="deleteRecJobPositionByPositionId" parameterType="Long">
        delete from rec_job_position where position_id = #{positionId}
    </delete>

    <delete id="deleteRecJobPositionByPositionIds" parameterType="String">
        delete from rec_job_position where position_id in 
        <foreach item="positionId" collection="array" open="(" separator="," close=")">
            #{positionId}
        </foreach>
    </delete>

    <!-- 更新职位审核状态 -->
    <update id="updatePositionAuditStatus" parameterType="RecJobPosition">
        update rec_job_position
        <trim prefix="SET" suffixOverrides=",">
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            update_time = sysdate()
        </trim>
        where position_id = #{positionId}
    </update>

    <!-- 更新职位发布状态 -->
    <update id="updatePositionPublishStatus" parameterType="RecJobPosition">
        update rec_job_position
        <trim prefix="SET" suffixOverrides=",">
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            update_time = sysdate()
        </trim>
        where position_id = #{positionId}
    </update>

    <!-- 增加职位浏览次数 -->
    <update id="increaseViewCount" parameterType="Long">
        update rec_job_position set view_count = view_count + 1 where position_id = #{positionId}
    </update>

    <!-- 增加职位申请次数 -->
    <update id="increaseApplyCount" parameterType="Long">
        update rec_job_position set apply_count = apply_count + 1 where position_id = #{positionId}
    </update>

    <!-- 统计职位总数 -->
    <select id="countTotalPositions" resultType="int">
        select count(*) from rec_job_position
    </select>

    <!-- 统计已发布职位数量 -->
    <select id="countPublishedPositions" resultType="int">
        select count(*) from rec_job_position where publish_status = 2
    </select>

    <!-- 统计待审核职位数量 -->
    <select id="countPendingAuditPositions" resultType="int">
        select count(*) from rec_job_position where audit_status = 0
    </select>

    <!-- 统计今日新增职位数量 -->
    <select id="countTodayNewPositions" resultType="int">
        select count(*) from rec_job_position
        where date_format(create_time,'%Y-%m-%d') = date_format(now(),'%Y-%m-%d')
    </select>

    <!-- 查询待审核职位列表 -->
    <select id="selectPendingAuditPositions" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        where p.audit_status = 0
        order by p.create_time desc
    </select>

    <!-- 查询热门职位列表 -->
    <select id="selectHotPositions" parameterType="Integer" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        where p.publish_status = 2 and p.audit_status = 1
        order by p.view_count desc, p.apply_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>

    <!-- 根据地区查询职位列表 -->
    <select id="selectPositionsByRegion" parameterType="String" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        where p.region_code = #{regionCode} and p.publish_status = 2 and p.audit_status = 1
        order by p.is_top desc, p.sort_order asc, p.create_time desc
    </select>

    <!-- 搜索职位 -->
    <select id="searchPositions" resultMap="RecJobPositionResult">
        <include refid="selectRecJobPositionVo"/>
        <where>
            p.publish_status = 2 and p.audit_status = 1
            <if test="keyword != null and keyword != ''">
                and (p.position_title like concat('%', #{keyword}, '%')
                     or p.job_description like concat('%', #{keyword}, '%')
                     or e.company_name like concat('%', #{keyword}, '%'))
            </if>
            <if test="regionCode != null and regionCode != ''">
                and p.region_code = #{regionCode}
            </if>
        </where>
        order by p.is_top desc, p.sort_order asc, p.create_time desc
    </select>

</mapper>
